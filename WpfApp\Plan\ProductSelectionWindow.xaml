<Window x:Class="WpfApp.Plan.ProductSelectionWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:local="clr-namespace:WpfApp.Plan"
        xmlns:app="clr-namespace:WpfApp"
        mc:Ignorable="d"
        Title="选择产品" Height="600" Width="900" 
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        Background="#F5F5F5">
    <Window.Resources>
        <app:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
    </Window.Resources>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 标题栏 -->
        <Border Grid.Row="0" Background="White" Padding="20,15">
            <TextBlock Text="选择产品" FontSize="18" FontWeight="Bold" Foreground="#333333"/>
        </Border>
        
        <!-- 搜索条件 -->
        <Border Grid.Row="1" Background="White" Margin="20,10,20,0" Padding="15">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- 第一行搜索条件 -->
                <StackPanel Grid.Row="0" Grid.Column="0" Margin="0,0,10,10">
                    <TextBlock Text="请输入产品编号/名称" FontSize="12" Foreground="#666666" Margin="0,0,0,5"/>
                    <TextBox x:Name="SearchTextBox" Text="{Binding SearchKeyword, UpdateSourceTrigger=PropertyChanged}"
                             Padding="8" Height="30" BorderThickness="1" BorderBrush="#DDDDDD"
                             TextChanged="SearchTextBox_TextChanged"/>
                </StackPanel>

                <StackPanel Grid.Row="0" Grid.Column="1" Margin="0,0,10,10">
                    <TextBlock Text="请选择单位" FontSize="12" Foreground="#666666" Margin="0,0,0,5"/>
                    <ComboBox ItemsSource="{Binding UnitOptions}" SelectedItem="{Binding SelectedUnit}"
                              Height="30" Padding="8" BorderThickness="1" BorderBrush="#DDDDDD"/>
                </StackPanel>

                <StackPanel Grid.Row="0" Grid.Column="2" Margin="0,0,10,10">
                    <TextBlock Text="请选择类型" FontSize="12" Foreground="#666666" Margin="0,0,0,5"/>
                    <ComboBox ItemsSource="{Binding ProductTypeOptions}" SelectedItem="{Binding SelectedProductType}"
                              Height="30" Padding="8" BorderThickness="1" BorderBrush="#DDDDDD"/>
                </StackPanel>

                <!-- 第二行搜索条件 -->
                <StackPanel Grid.Row="1" Grid.Column="0" Margin="0,0,10,10">
                    <TextBlock Text="请选择产品属性" FontSize="12" Foreground="#666666" Margin="0,0,0,5"/>
                    <ComboBox ItemsSource="{Binding ProductAttributeOptions}" SelectedItem="{Binding SelectedProductAttribute}"
                              Height="30" Padding="8" BorderThickness="1" BorderBrush="#DDDDDD"/>
                </StackPanel>

                <!-- 搜索和重置按钮 -->
                <StackPanel Grid.Row="1" Grid.Column="1" Grid.ColumnSpan="2" Orientation="Horizontal" VerticalAlignment="Bottom" HorizontalAlignment="Right" Margin="0,0,0,10">
                    <Button Content="查询" Command="{Binding SearchCommand}"
                            Background="#3080FE" Foreground="White" Width="70" Height="30" Margin="0,0,5,0">
                        <Button.Resources>
                            <Style TargetType="Border">
                                <Setter Property="CornerRadius" Value="2"/>
                            </Style>
                        </Button.Resources>
                    </Button>
                    <Button Content="重置" Command="{Binding ResetCommand}"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Width="70" Height="30" Margin="0,0,5,0">
                        <Button.Resources>
                            <Style TargetType="Border">
                                <Setter Property="CornerRadius" Value="2"/>
                            </Style>
                        </Button.Resources>
                    </Button>
                    <Button Content="刷新" Command="{Binding RefreshCommand}"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Width="70" Height="30">
                        <Button.Resources>
                            <Style TargetType="Border">
                                <Setter Property="CornerRadius" Value="2"/>
                            </Style>
                        </Button.Resources>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>

        <!-- 错误信息显示 -->
        <Border Grid.Row="2" Background="#FFEBEE" Margin="20,10,20,0" Padding="15"
                Visibility="{Binding HasError, Converter={StaticResource BooleanToVisibilityConverter}}">
            <StackPanel Orientation="Horizontal">
                <materialDesign:PackIcon Kind="AlertCircle" Foreground="#D32F2F" VerticalAlignment="Center" Margin="0,0,10,0"/>
                <TextBlock Text="{Binding ErrorMessage}" Foreground="#D32F2F" VerticalAlignment="Center"/>
            </StackPanel>
        </Border>

        <!-- 产品列表 -->
        <Border Grid.Row="3" Background="White" Margin="20,10,20,0">
            <Grid>
                <DataGrid ItemsSource="{Binding Products}" AutoGenerateColumns="False"
                          CanUserAddRows="False" CanUserDeleteRows="False"
                          CanUserResizeRows="False" IsReadOnly="True"
                          SelectionMode="Single" SelectedItem="{Binding SelectedProduct}"
                          HeadersVisibility="Column"
                          BorderThickness="0" GridLinesVisibility="Horizontal"
                          Background="White" RowBackground="White" AlternatingRowBackground="#F9F9F9"
                          Visibility="{Binding HasError, Converter={StaticResource BooleanToVisibilityConverter}, ConverterParameter=Invert}">
                    <DataGrid.Resources>
                        <Style TargetType="{x:Type DataGridColumnHeader}">
                            <Setter Property="Background" Value="White"/>
                            <Setter Property="Padding" Value="10,8"/>
                            <Setter Property="BorderBrush" Value="#E8E8E8"/>
                            <Setter Property="BorderThickness" Value="0,0,0,1"/>
                            <Setter Property="FontWeight" Value="Bold"/>
                            <Setter Property="Foreground" Value="#333333"/>
                        </Style>
                        <Style TargetType="{x:Type DataGridRow}">
                            <Setter Property="Height" Value="40"/>
                            <Style.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="#E3F2FD"/>
                                </Trigger>
                                <Trigger Property="IsSelected" Value="True">
                                    <Setter Property="Background" Value="#BBDEFB"/>
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                        <Style TargetType="{x:Type DataGridCell}">
                            <Setter Property="BorderThickness" Value="0"/>
                            <Setter Property="Padding" Value="10,8"/>
                            <Setter Property="VerticalAlignment" Value="Center"/>
                        </Style>
                    </DataGrid.Resources>
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="序号" Binding="{Binding RowNumber}" Width="60"/>
                        <DataGridTextColumn Header="产品编号" Binding="{Binding ProductCode}" Width="120"/>
                        <DataGridTextColumn Header="产品名称" Binding="{Binding ProductName}" Width="150"/>
                        <DataGridTextColumn Header="规格型号" Binding="{Binding Specification}" Width="120"/>
                        <DataGridTextColumn Header="单位" Binding="{Binding Unit}" Width="80"/>
                        <DataGridTextColumn Header="产品类型" Binding="{Binding ProductType}" Width="120"/>
                        <DataGridTextColumn Header="产品属性" Binding="{Binding ProductAttribute}" Width="120"/>
                    </DataGrid.Columns>
                </DataGrid>
                
                <!-- 加载指示器 -->
                <Grid Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                      Background="#80FFFFFF">
                    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                        <ProgressBar IsIndeterminate="True" Width="50" Height="50"
                                     Style="{StaticResource MaterialDesignCircularProgressBar}"/>
                        <TextBlock Text="加载中..." Margin="0,10,0,0" HorizontalAlignment="Center"/>
                    </StackPanel>
                </Grid>

                <!-- 空数据提示 -->
                <Grid Visibility="{Binding IsEmpty, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                        <materialDesign:PackIcon Kind="DatabaseOff" Width="64" Height="64" Foreground="#CCCCCC"/>
                        <TextBlock Text="暂无数据" FontSize="16" Foreground="#999999" Margin="0,10,0,0" HorizontalAlignment="Center"/>
                        <TextBlock Text="请尝试调整搜索条件或点击刷新按钮" FontSize="12" Foreground="#CCCCCC" Margin="0,5,0,0" HorizontalAlignment="Center"/>
                    </StackPanel>
                </Grid>
            </Grid>
        </Border>
        
        <!-- 分页控件 -->
        <Border Grid.Row="4" Background="White" Margin="20,0,20,0" Padding="15,10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" VerticalAlignment="Center">
                    <Run Text="共"/>
                    <Run Text="{Binding TotalCount}"/>
                    <Run Text="条"/>
                </TextBlock>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                    <Button Content="上一页" Command="{Binding PrevPageCommand}" 
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Width="70" Height="30" Margin="0,0,10,0"/>
                    
                    <ItemsControl ItemsSource="{Binding PageButtons}">
                        <ItemsControl.ItemsPanel>
                            <ItemsPanelTemplate>
                                <StackPanel Orientation="Horizontal"/>
                            </ItemsPanelTemplate>
                        </ItemsControl.ItemsPanel>
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <Button Content="{Binding PageNumber}" 
                                        Command="{Binding DataContext.GoToPageCommand, RelativeSource={RelativeSource AncestorType=Window}}"
                                        CommandParameter="{Binding PageNumber}"
                                        Width="30" Height="30" Margin="2,0"
                                        Background="{Binding IsSelected, Converter={StaticResource BooleanToVisibilityConverter}}"
                                        Style="{StaticResource MaterialDesignOutlinedButton}"/>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                    
                    <Button Content="下一页" Command="{Binding NextPageCommand}"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Width="70" Height="30" Margin="10,0,0,0"/>
                    
                    <TextBlock Text="前往" VerticalAlignment="Center" Margin="20,0,5,0"/>
                    <TextBox Text="{Binding GoToPage}" Width="40" Height="25" Margin="0,0,5,0"/>
                    <TextBlock Text="页" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <Button Content="确定" Command="{Binding GoToPageCommand}" CommandParameter="{Binding GoToPage}"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Width="50" Height="25"/>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- 按钮栏 -->
        <StackPanel Grid.Row="5" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,0,20,20">
            <Button Content="取消" Command="{Binding CancelCommand}" 
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    Width="70" Height="30" Margin="0,0,10,0"/>
            <Button Content="确定" Command="{Binding ConfirmCommand}"
                    Background="#3080FE" Foreground="White" Width="70" Height="30">
                <Button.Resources>
                    <Style TargetType="Border">
                        <Setter Property="CornerRadius" Value="2"/>
                    </Style>
                </Button.Resources>
            </Button>
        </StackPanel>
    </Grid>
</Window>
